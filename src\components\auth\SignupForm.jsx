import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  FiUser,
  FiMail,
  FiCreditCard,
} from "react-icons/fi";
import { signupUser } from "../../store/slices/SignupSlice";
import { FormField, TextInput, PasswordInput } from "../ui/FormComponents";
import { BasicCard } from "../ui/cards";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css"; // default styling for phone input

const SignupForm = ({ role }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.signup);

  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    cnic: "",
    passport: "",
    password: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handlePhoneChange = (value, country) => {
    setFormData((prev) => ({ ...prev, phone: value }));
    if (errors.phone) setErrors((prev) => ({ ...prev, phone: "" }));
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.fullName.trim()) newErrors.fullName = "Full name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!formData.phone) newErrors.phone = "Phone number is required";
    if (!formData.password) newErrors.password = "Password is required";
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      await dispatch(signupUser({ ...formData, role })).unwrap();
      navigate("/email-verification");
    } catch (err) {
      console.error("Signup failed:", err);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto">
      <BasicCard>
        <div className="bg-gradient-to-r from-violet-600 to-indigo-600 px-8 py-6 text-white">
          <h2 className="text-3xl font-bold">Create your account</h2>
          <p className="text-violet-100">
            Join EduFair as a {role} and start your journey today.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-8 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700 text-sm">
              {error}
            </div>
          )}

          {/* Full Name + Email */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField label="Full Name" error={errors.fullName} required>
              <TextInput
                name="fullName"
                placeholder="Enter your full name"
                value={formData.fullName}
                onChange={handleChange}
                icon={FiUser}
                error={errors.fullName}
              />
            </FormField>

            <FormField label="Email Address" error={errors.email} required>
              <TextInput
                type="email"
                name="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleChange}
                icon={FiMail}
                error={errors.email}
              />
            </FormField>
          </div>

          {/* Professional Country Code + Phone Input */}
          <FormField label="Phone Number" error={errors.phone} required>
            <PhoneInput
              country={"pk"}
              value={formData.phone}
              onChange={handlePhoneChange}
              inputClass={`!w-full !py-3 !text-base !rounded-xl !pl-12 ${
                errors.phone
                  ? "!border-red-400 focus:!border-red-500"
                  : "!border-gray-300 focus:!border-violet-500"
              }`}
              buttonClass="!rounded-l-xl !border-gray-300"
              dropdownClass="!bg-white !text-gray-900 !rounded-xl"
              enableSearch
              disableSearchIcon={false}
            />
          </FormField>

          {/* CNIC / Passport */}
          <FormField label="CNIC / Passport" error={errors.cnic || errors.passport}>
            <TextInput
              placeholder="Enter your CNIC or Passport Number"
              value={formData.cnic || formData.passport}
              onChange={handleChange}
              name="cnic"
              icon={FiCreditCard}
              error={errors.cnic || errors.passport}
            />
          </FormField>

          {/* Passwords */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField label="Password" error={errors.password}>
              <PasswordInput
                name="password"
                placeholder="Create a strong password"
                value={formData.password}
                onChange={handleChange}
                error={errors.password}
              />
            </FormField>

            <FormField label="Confirm Password" error={errors.confirmPassword}>
              <PasswordInput
                name="confirmPassword"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={errors.confirmPassword}
              />
            </FormField>
          </div>

          {/* Submit */}
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-violet-600 hover:bg-violet-700 disabled:bg-violet-300 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200"
          >
            {loading ? "Creating account..." : "Create account"}
          </button>

          {/* Login redirect */}
          <p className="text-center text-sm text-gray-600">
            Already have an account?{" "}
            <button
              type="button"
              onClick={() => navigate("/login")}
              className="text-violet-600 font-semibold hover:text-violet-700"
            >
              Sign in
            </button>
          </p>
        </form>
      </BasicCard>
    </div>
  );
};

export default SignupForm;
