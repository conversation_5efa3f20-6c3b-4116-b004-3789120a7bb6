import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { FiUser, FiMail, FiCreditCard } from 'react-icons/fi';
import { signupUser } from '../../store/slices/SignupSlice';
import { FormField, TextInput, PasswordInput } from '../ui/FormComponents';


const SignupForm = ({ role }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.signup);

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    cnic: '',
    passport: '',
    password: '',
    confirmPassword: '',
  });

  const [errors, setErrors] = useState({});
  const [countryCode, setCountryCode] = useState('PK');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleIdentityChange = (e) => {
    const value = e.target.value;
    if (countryCode === 'PK') {
      setFormData(prev => ({ ...prev, cnic: value, passport: '' }));
    } else {
      setFormData(prev => ({ ...prev, passport: value, cnic: '' }));
    }
    
    if (errors.cnic || errors.passport) {
      setErrors(prev => ({ ...prev, cnic: '', passport: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone) newErrors.phone = 'Phone number is required';
    if (!formData.password) newErrors.password = 'Password is required';
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (countryCode === 'PK') {
      if (!formData.cnic && !formData.passport) {
        newErrors.cnic = 'CNIC or Passport is required';
      }
    } else {
      if (!formData.passport) {
        newErrors.passport = 'Passport number is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const submitData = {
      ...formData,
      role,
      countryCode,
    };

    try {
      await dispatch(signupUser(submitData)).unwrap();
      navigate('/email-verification');
    } catch (err) {
      console.error('Signup failed:', err);
    }
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-3">
            Create your account
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-base">
            Join EduFair as a {role} and start your journey today.
          </p>
        </div>

        <div className="space-y-6">
          {/* Row 1: Full Name and Email */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField label="Full Name" error={errors.fullName}>
              <TextInput
                name="fullName"
                placeholder="Enter your full name"
                value={formData.fullName}
                onChange={handleChange}
                icon={FiUser}
                error={errors.fullName}
                className="text-base"
              />
            </FormField>

            <FormField label="Email Address" error={errors.email}>
              <TextInput
                type="email"
                name="email"
                placeholder="Enter your email address"
                value={formData.email}
                onChange={handleChange}
                icon={FiMail}
                error={errors.email}
                className="text-base"
              />
            </FormField>
          </div>

          {/* Row 2: Phone Number (Full Width) */}
          <FormField label="Phone Number" error={errors.phone}>
            <div className="relative">
              {/* Country Flag Selector */}
              <select
                value={countryCode}
                onChange={(e) => setCountryCode(e.target.value)}
                className="absolute left-0 top-0 h-12 w-16 border-2 border-r-0 border-gray-300 dark:border-gray-600 rounded-l-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500 hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-200 z-10 cursor-pointer text-center"
              >
                <option value="PK">🇵🇰</option>
                <option value="US">🇺🇸</option>
                <option value="GB">🇬🇧</option>
                <option value="IN">🇮🇳</option>
                <option value="AE">🇦🇪</option>
                <option value="SA">🇸🇦</option>
                <option value="CA">🇨🇦</option>
                <option value="AU">🇦🇺</option>
                <option value="DE">🇩🇪</option>
                <option value="FR">🇫🇷</option>
                <option value="IT">🇮🇹</option>
                <option value="ES">🇪🇸</option>
                <option value="NL">🇳🇱</option>
                <option value="SE">🇸🇪</option>
                <option value="NO">🇳🇴</option>
                <option value="DK">🇩🇰</option>
                <option value="FI">🇫🇮</option>
                <option value="CH">🇨🇭</option>
                <option value="AT">🇦🇹</option>
                <option value="BE">🇧🇪</option>
                <option value="IE">🇮🇪</option>
                <option value="PT">🇵🇹</option>
                <option value="GR">🇬🇷</option>
                <option value="PL">🇵🇱</option>
                <option value="CZ">🇨🇿</option>
                <option value="HU">🇭🇺</option>
                <option value="RO">🇷🇴</option>
                <option value="BG">🇧🇬</option>
                <option value="HR">🇭🇷</option>
                <option value="SI">🇸🇮</option>
                <option value="SK">🇸🇰</option>
                <option value="EE">🇪🇪</option>
                <option value="LV">🇱🇻</option>
                <option value="LT">🇱🇹</option>
                <option value="LU">🇱🇺</option>
                <option value="MT">🇲🇹</option>
                <option value="CY">🇨🇾</option>
                <option value="JP">🇯🇵</option>
                <option value="KR">🇰🇷</option>
                <option value="CN">🇨🇳</option>
                <option value="SG">🇸🇬</option>
                <option value="MY">🇲🇾</option>
                <option value="TH">🇹🇭</option>
                <option value="ID">🇮🇩</option>
                <option value="PH">🇵🇭</option>
                <option value="VN">🇻🇳</option>
                <option value="BD">🇧🇩</option>
                <option value="LK">🇱🇰</option>
                <option value="NP">🇳🇵</option>
                <option value="AF">🇦🇫</option>
                <option value="IR">🇮🇷</option>
                <option value="IQ">🇮🇶</option>
                <option value="SY">🇸🇾</option>
                <option value="JO">🇯🇴</option>
                <option value="LB">🇱🇧</option>
                <option value="TR">🇹🇷</option>
                <option value="EG">🇪🇬</option>
                <option value="MA">🇲🇦</option>
                <option value="DZ">🇩🇿</option>
                <option value="TN">🇹🇳</option>
                <option value="LY">🇱🇾</option>
                <option value="SD">🇸🇩</option>
                <option value="ET">🇪🇹</option>
                <option value="KE">🇰🇪</option>
                <option value="UG">🇺🇬</option>
                <option value="TZ">🇹🇿</option>
                <option value="ZA">🇿🇦</option>
                <option value="NG">🇳🇬</option>
                <option value="GH">🇬🇭</option>
                <option value="CI">🇨🇮</option>
                <option value="SN">🇸🇳</option>
                <option value="ML">🇲🇱</option>
                <option value="BF">🇧🇫</option>
                <option value="NE">🇳🇪</option>
                <option value="TD">🇹🇩</option>
                <option value="CM">🇨🇲</option>
                <option value="CF">🇨🇫</option>
                <option value="GA">🇬🇦</option>
                <option value="CG">🇨🇬</option>
                <option value="CD">🇨🇩</option>
                <option value="AO">🇦🇴</option>
                <option value="ZM">🇿🇲</option>
                <option value="ZW">🇿🇼</option>
                <option value="BW">🇧🇼</option>
                <option value="NA">🇳🇦</option>
                <option value="MZ">🇲🇿</option>
                <option value="MW">🇲🇼</option>
                <option value="MG">🇲🇬</option>
                <option value="MU">🇲🇺</option>
                <option value="SC">🇸🇨</option>
                <option value="BR">🇧🇷</option>
                <option value="AR">🇦🇷</option>
                <option value="CL">🇨🇱</option>
                <option value="PE">🇵🇪</option>
                <option value="CO">🇨🇴</option>
                <option value="VE">🇻🇪</option>
                <option value="EC">🇪🇨</option>
                <option value="BO">🇧🇴</option>
                <option value="PY">🇵🇾</option>
                <option value="UY">🇺🇾</option>
                <option value="GY">🇬🇾</option>
                <option value="SR">🇸🇷</option>
                <option value="MX">🇲🇽</option>
                <option value="GT">🇬🇹</option>
                <option value="BZ">🇧🇿</option>
                <option value="SV">🇸🇻</option>
                <option value="HN">🇭🇳</option>
                <option value="NI">🇳🇮</option>
                <option value="CR">🇨🇷</option>
                <option value="PA">🇵🇦</option>
                <option value="CU">🇨🇺</option>
                <option value="JM">🇯🇲</option>
                <option value="HT">🇭🇹</option>
                <option value="DO">🇩🇴</option>
                <option value="TT">🇹🇹</option>
                <option value="BB">🇧🇧</option>
                <option value="GD">🇬🇩</option>
                <option value="LC">🇱🇨</option>
                <option value="VC">🇻🇨</option>
                <option value="DM">🇩🇲</option>
                <option value="KN">🇰🇳</option>
                <option value="AG">🇦🇬</option>
                <option value="BS">🇧🇸</option>
                <option value="RU">🇷🇺</option>
                <option value="UA">🇺🇦</option>
                <option value="BY">🇧🇾</option>
                <option value="MD">🇲🇩</option>
                <option value="GE">🇬🇪</option>
                <option value="AM">🇦🇲</option>
                <option value="AZ">🇦🇿</option>
                <option value="KZ">🇰🇿</option>
                <option value="UZ">🇺🇿</option>
                <option value="TM">🇹🇲</option>
                <option value="KG">🇰🇬</option>
                <option value="TJ">🇹🇯</option>
                <option value="MN">🇲🇳</option>
                <option value="NZ">🇳🇿</option>
                <option value="FJ">🇫🇯</option>
                <option value="PG">🇵🇬</option>
                <option value="SB">🇸🇧</option>
                <option value="VU">🇻🇺</option>
                <option value="NC">🇳🇨</option>
                <option value="PF">🇵🇫</option>
                <option value="WS">🇼🇸</option>
                <option value="TO">🇹🇴</option>
                <option value="TV">🇹🇻</option>
                <option value="NR">🇳🇷</option>
                <option value="KI">🇰🇮</option>
                <option value="MH">🇲🇭</option>
                <option value="FM">🇫🇲</option>
                <option value="PW">🇵🇼</option>
              </select>

              {/* Phone Number Input */}
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, phone: e.target.value }));
                  if (errors.phone) {
                    setErrors(prev => ({ ...prev, phone: '' }));
                  }
                }}
                placeholder="Enter your phone number"
                className={`
                  w-full pl-16 pr-4 py-3 border-2 rounded-xl transition-all duration-200 text-base min-h-[48px]
                  bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                  placeholder-gray-500 dark:placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-violet-500/50
                  ${errors.phone
                    ? 'border-red-400 dark:border-red-500 focus:border-red-500'
                    : 'border-gray-300 dark:border-gray-600 focus:border-violet-500 hover:border-gray-400 dark:hover:border-gray-500'
                  }
                `}
              />
            </div>
          </FormField>

          {/* Row 3: CNIC/Passport (Full Width) */}
          <FormField 
            label={countryCode === 'PK' ? 'CNIC or Passport' : 'Passport Number'} 
            error={errors.cnic || errors.passport}
          >
            <TextInput
              placeholder={countryCode === 'PK' ? 'Enter your CNIC or Passport Number' : 'Enter your Passport Number'}
              value={countryCode === 'PK' ? formData.cnic || formData.passport : formData.passport}
              onChange={handleIdentityChange}
              icon={FiCreditCard}
              error={errors.cnic || errors.passport}
              className="text-base"
            />
          </FormField>

          {/* Row 4: Password and Confirm Password */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField label="Password" error={errors.password}>
              <PasswordInput
                name="password"
                placeholder="Create a strong password"
                value={formData.password}
                onChange={handleChange}
                error={errors.password}
                className="text-base"
              />
            </FormField>

            <FormField label="Confirm Password" error={errors.confirmPassword}>
              <PasswordInput
                name="confirmPassword"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={errors.confirmPassword}
                className="text-base"
              />
            </FormField>
          </div>
        </div>

        <div className="space-y-6 mt-8">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
              <div className="flex items-center text-red-700 dark:text-red-400 text-sm">
                <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                {error}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-violet-600 hover:bg-violet-700 active:bg-violet-800 disabled:bg-violet-300 dark:disabled:bg-violet-800 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-violet-500/50 transform hover:scale-[1.02] disabled:transform-none disabled:cursor-not-allowed min-h-[56px] text-base"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating account...
              </div>
            ) : (
              'Create account'
            )}
          </button>

          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Already have an account?{' '}
              <button
                type="button"
                onClick={() => navigate('/Login')}
                className="text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 font-semibold transition-colors duration-200"
              >
                Sign in
              </button>
            </p>
          </div>
        </div>
      </form>
    </div>
  );
};

export default SignupForm;
